interface LoadingSkeletonProps {
  className?: string;
}

export function LoadingSkeleton({ className = "" }: LoadingSkeletonProps) {
  return (
    <div className={`animate-pulse bg-gray-200 rounded ${className}`}>
      <div className="h-full w-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-size-200 animate-shimmer rounded"></div>
    </div>
  );
}

export function LandingPageSkeleton() {
  return (
    <div className="min-h-screen w-full relative overflow-hidden bg-gray-50">
      {/* Header Skeleton */}
      <nav className="flex items-center justify-between px-8 py-5 h-[73px]">
        <div className="flex items-center space-x-3">
          <LoadingSkeleton className="w-6 h-6 rounded-full" />
          <LoadingSkeleton className="w-24 h-6" />
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <LoadingSkeleton className="w-16 h-4" />
          <LoadingSkeleton className="w-20 h-4" />
          <LoadingSkeleton className="w-14 h-4" />
        </div>
        
        <div className="flex items-center space-x-4">
          <LoadingSkeleton className="w-16 h-4" />
          <LoadingSkeleton className="w-20 h-10 rounded-full" />
        </div>
      </nav>

      {/* Main Content Skeleton */}
      <div className="relative h-[calc(100vh-73px)] flex items-center justify-center px-8 py-12">
        <div className="w-full max-w-7xl flex items-center justify-between">
          
          {/* Left Side Skeleton */}
          <div className="w-[422px] h-[534px] bg-white rounded-lg p-10">
            <LoadingSkeleton className="w-14 h-16 rounded-full mb-8" />
            <div className="space-y-6">
              <div className="space-y-2">
                <LoadingSkeleton className="w-full h-8" />
                <LoadingSkeleton className="w-4/5 h-8" />
              </div>
              <div className="space-y-2">
                <LoadingSkeleton className="w-full h-4" />
                <LoadingSkeleton className="w-5/6 h-4" />
              </div>
            </div>
          </div>

          {/* Right Side Skeleton */}
          <div className="w-[417px] h-[532px] bg-white rounded-lg p-8">
            <div className="max-w-[354px] mx-auto pt-12">
              <div className="mb-8">
                <LoadingSkeleton className="w-48 h-6 mb-2" />
                <LoadingSkeleton className="w-64 h-4" />
              </div>
              
              <div className="space-y-6">
                <div>
                  <LoadingSkeleton className="w-20 h-3 mb-2" />
                  <LoadingSkeleton className="w-full h-11" />
                </div>
                <div>
                  <LoadingSkeleton className="w-24 h-3 mb-2" />
                  <LoadingSkeleton className="w-full h-11" />
                </div>
                <div>
                  <LoadingSkeleton className="w-16 h-3 mb-2" />
                  <LoadingSkeleton className="w-full h-11" />
                </div>
                
                <div className="flex items-center space-x-3 pt-4">
                  <LoadingSkeleton className="w-4 h-4" />
                  <LoadingSkeleton className="w-32 h-3" />
                </div>
                
                <LoadingSkeleton className="w-full h-11 rounded" />
                
                <div className="text-center pt-4">
                  <LoadingSkeleton className="w-48 h-3 mx-auto" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
