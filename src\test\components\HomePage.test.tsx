import HomePage from '@/app/page';
import { ThemeProvider } from '@/providers/theme-provider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';

// Create a wrapper for providers
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light">
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  );
  TestWrapper.displayName = 'TestWrapper';

  return TestWrapper;
};

describe('HomePage', () => {
  it('renders the hero section', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <HomePage />
      </Wrapper>
    );

    expect(screen.getByText('Welcome to EduPro')).toBeInTheDocument();
    expect(screen.getByText('Learn, Grow, Excel')).toBeInTheDocument();
  });

  it('renders the features section', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <HomePage />
      </Wrapper>
    );

    expect(screen.getByText('Features')).toBeInTheDocument();
    expect(screen.getByText('Interactive Learning')).toBeInTheDocument();
    expect(screen.getByText('Progress Tracking')).toBeInTheDocument();
    expect(screen.getByText('Expert Instructors')).toBeInTheDocument();
  });
});
