import { http, HttpResponse } from 'msw';

export const handlers = [
  // Auth endpoints
  http.post('/api/auth/login', () => {
    return HttpResponse.json({
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      },
      session: {
        access_token: 'fake-access-token',
        refresh_token: 'fake-refresh-token',
      },
    });
  }),

  http.post('/api/auth/signup', () => {
    return HttpResponse.json({
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      },
    });
  }),

  http.post('/api/auth/logout', () => {
    return HttpResponse.json({ success: true });
  }),

  // Mock Supabase API
  http.post('*/auth/v1/token', () => {
    return HttpResponse.json({
      access_token: 'fake-access-token',
      token_type: 'bearer',
      expires_in: 3600,
      refresh_token: 'fake-refresh-token',
      user: {
        id: '1',
        email: '<EMAIL>',
        email_confirmed_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    });
  }),
];
