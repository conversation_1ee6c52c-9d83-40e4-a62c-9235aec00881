import { create } from "zustand";

interface AppStore {
  theme: "light" | "dark" | "system";
  sidebar: {
    isOpen: boolean;
    isMobile: boolean;
  };
  notifications: Array<{
    id: string;
    type: "success" | "error" | "warning" | "info";
    title: string;
    message: string;
    timestamp: Date;
  }>;
  setTheme: (theme: "light" | "dark" | "system") => void;
  setSidebar: (sidebar: Partial<AppStore["sidebar"]>) => void;
  addNotification: (notification: Omit<AppStore["notifications"][0], "id" | "timestamp">) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

export const useAppStore = create<AppStore>((set) => ({
  theme: "system",
  sidebar: {
    isOpen: false,
    isMobile: false,
  },
  notifications: [],
  
  setTheme: (theme) => set({ theme }),
  
  setSidebar: (sidebar) =>
    set((state) => ({
      sidebar: { ...state.sidebar, ...sidebar },
    })),
  
  addNotification: (notification) =>
    set((state) => ({
      notifications: [
        ...state.notifications,
        {
          ...notification,
          id: Math.random().toString(36).substr(2, 9),
          timestamp: new Date(),
        },
      ],
    })),
  
  removeNotification: (id) =>
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
    })),
  
  clearNotifications: () => set({ notifications: [] }),
}));

// Selectors
export const useTheme = () => useAppStore((state) => state.theme);
export const useSidebar = () => useAppStore((state) => state.sidebar);
export const useNotifications = () => useAppStore((state) => state.notifications);
