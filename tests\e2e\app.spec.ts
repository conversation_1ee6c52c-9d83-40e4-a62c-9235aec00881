import { expect, test } from '@playwright/test';

test.describe('EduPro App', () => {
  test('should display the homepage correctly', async ({ page }) => {
    await page.goto('/');

    // Check if the main heading is visible
    await expect(page.getByText('Welcome to EduPro')).toBeVisible();
    await expect(page.getByText('Learn, Grow, Excel')).toBeVisible();

    // Check if the navigation is present
    await expect(page.getByRole('link', { name: 'EduPro' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Login' })).toBeVisible();

    // Check if theme toggle is present
    await expect(page.getByRole('button', { name: 'Toggle theme' })).toBeVisible();

    // Check if features section is visible
    await expect(page.getByText('Features')).toBeVisible();
    await expect(page.getByText('Interactive Learning')).toBeVisible();
    await expect(page.getByText('Progress Tracking')).toBeVisible();
    await expect(page.getByText('Expert Instructors')).toBeVisible();
  });

  test('should navigate to login page', async ({ page }) => {
    await page.goto('/');
    
    // Click the login link
    await page.getByRole('link', { name: 'Login' }).click();
    
    // Check if we're on the login page
    await expect(page).toHaveURL('/auth/login');
    await expect(page.getByText('Welcome back')).toBeVisible();
    await expect(page.getByText('Sign in to your account')).toBeVisible();
  });

  test('should toggle between login and signup', async ({ page }) => {
    await page.goto('/auth/login');

    // Check initial login state
    await expect(page.getByText('Welcome back')).toBeVisible();

    // Click signup link
    await page.getByText('Sign up').click();

    // Should switch to signup mode
    await expect(page.getByText('Create an account')).toBeVisible();
    await expect(page.getByText('Enter your details to get started')).toBeVisible();

    // Click back to login
    await page.getByText('Sign in').click();

    // Should switch back to login mode
    await expect(page.getByText('Welcome back')).toBeVisible();
  });

  test('should validate form fields', async ({ page }) => {
    await page.goto('/auth/login');

    // Try to submit empty form
    await page.getByRole('button', { name: 'Sign in' }).click();

    // Should show validation errors
    await expect(page.getByText('Email is required')).toBeVisible();
    await expect(page.getByText('Password must be at least 6 characters')).toBeVisible();
  });

  test('should toggle theme', async ({ page }) => {
    await page.goto('/');

    // Get the current theme
    const html = page.locator('html');
    const initialClass = await html.getAttribute('class');

    // Click theme toggle
    await page.getByRole('button', { name: 'Toggle theme' }).click();

    // Wait for theme change
    await page.waitForTimeout(100);

    // Check if theme has changed
    const newClass = await html.getAttribute('class');
    expect(newClass).not.toBe(initialClass);
  });
});
