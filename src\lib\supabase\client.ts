import { Database } from "@/types/supabase";
import { createBrowserClient } from "@supabase/ssr";

// Use fallback values for development if environment variables are not set
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "https://example.supabase.co";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "example-key";

// In production, we still want to enforce environment variables
if (process.env.NODE_ENV === "production" && (!supabaseUrl || !supabaseKey)) {
  throw new Error("Missing Supabase environment variables in production");
}

/**
 * Creates a Supabase client for browser usage
 * This should be used in Client Components
 */
export function createClient() {
  return createBrowserClient<Database>(supabaseUrl, supabaseKey);
}

/**
 * Singleton instance of the Supabase client for browser usage
 * Use this in Client Components where you need the client
 */
export const supabase = createClient();
