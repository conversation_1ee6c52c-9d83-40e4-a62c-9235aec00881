{"name": "edupro", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "supabase:types": "supabase gen types typescript --project-id \"$SUPABASE_PROJECT_ID\" --schema public > src/types/supabase.ts", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@supabase/ssr": "^0.5.0", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.45.0", "@tanstack/react-query-devtools": "^5.45.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.400.0", "next": "^15.1.0", "next-themes": "^0.4.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "sonner": "^2.0.3", "tailwind-merge": "^2.3.0", "zod": "^3.25.36", "zustand": "^4.5.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.2", "@playwright/test": "^1.52.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "^9.5.0", "eslint-config-next": "^15.1.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lint-staged": "^16.1.0", "msw": "^2.8.5", "postcss": "^8.4.39", "prettier": "^3.3.0", "tailwindcss": "^4.0.0-alpha.15", "typescript": "^5.6.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}}