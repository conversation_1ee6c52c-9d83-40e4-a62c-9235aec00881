import { Metadata } from 'next';

interface LandingMetaProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
}

// Generate metadata for the landing page
export function generateLandingMetadata({
  title = "Acme Co - Unlock Your Potential",
  description = "Experience a seamless platform designed to elevate your productivity and drive success. Join thousands of users who trust Acme Co.",
  keywords = "productivity, platform, success, business, acme, growth, efficiency",
  image = "/images/hero/main-background.png"
}: LandingMetaProps = {}): Metadata {
  return {
    title,
    description,
    keywords,
    authors: [{ name: "Acme Co" }],
    robots: {
      index: true,
      follow: true,
    },
    openGraph: {
      type: 'website',
      title,
      description,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [image],
    },
  };
}

// Component for client-side meta (for JSON-LD structured data)
export function LandingMeta({
  description = "Experience a seamless platform designed to elevate your productivity and drive success. Join thousands of users who trust Acme Co."
}: Omit<LandingMetaProps, 'title'>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Acme Co",
    "description": description,
    "url": typeof window !== 'undefined' ? window.location.origin : '',
    "logo": "/images/ui/brand-logo.png",
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}
