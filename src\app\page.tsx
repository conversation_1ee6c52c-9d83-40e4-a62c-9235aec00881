"use client";

import LandingPage from "@/components/landing/landing-page";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function HomePage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      // If user is authenticated, redirect to dashboard
      router.replace("/dashboard");
    }
  }, [user, loading, router]);

  // Show landing page immediately while checking authentication
  // The landing page will handle the loading state internally
  if (loading || !user) {
    return <LandingPage />;
  }

  // Redirect authenticated users to dashboard
  if (user) {
    router.replace("/dashboard");
    return null;
  }

  // Return the landing page by default
  return <LandingPage />;
}