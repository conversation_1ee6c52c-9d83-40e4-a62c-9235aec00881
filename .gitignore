# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Next.js
.next/
out/
build/
dist/

# Environment variables
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Turbo
.turbo/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# OSX
.DS_Store

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# ESLint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Testing
coverage/
.jest-cache/

# Bundle analyzer
.bundle-analyzer/
