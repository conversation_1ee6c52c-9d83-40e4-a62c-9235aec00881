# Images Directory Structure

This directory contains all static images for the EduPro application. Images are organized by their usage context for better maintainability.

## Directory Structure

```
images/
├── auth/           # Authentication related images (login backgrounds, illustrations)
├── avatars/        # User profile pictures and default avatars
├── backgrounds/    # Background images for various sections
├── courses/        # Course thumbnails, category images, and course-related visuals
├── hero/           # Hero section images and main landing page visuals
├── icons/          # Application icons, feature icons, and UI icons
├── logos/          # Company logos, partner logos, and brand assets
└── ui/             # General UI elements and interface graphics
```

## Usage Guidelines

### Image Formats
- **PNG**: For images with transparency or simple graphics
- **JPEG/JPG**: For photos and complex images without transparency
- **SVG**: For scalable icons and simple graphics
- **WebP**: Preferred for modern browsers (better compression)

### Naming Conventions
- Use kebab-case for file names: `login-background.jpg`
- Include descriptive names: `course-javascript-thumbnail.png`
- Add size suffixes when multiple sizes exist: `logo-large.svg`, `logo-small.svg`

### Optimization
- Optimize images before adding them to the project
- Consider using Next.js Image component for automatic optimization
- Provide multiple sizes for responsive images when needed

### Access in Code
Images in the public directory can be accessed using absolute paths starting with `/`:

```tsx
// Example usage
<img src="/images/logos/edupro-logo.svg" alt="EduPro Logo" />

// With Next.js Image component
import Image from 'next/image';
<Image src="/images/hero/welcome-banner.jpg" alt="Welcome" width={800} height={400} />
```

## Best Practices

1. **Responsive Images**: Provide multiple sizes for different screen resolutions
2. **Alt Text**: Always include descriptive alt text for accessibility
3. **Lazy Loading**: Use Next.js Image component for automatic lazy loading
4. **File Size**: Keep images optimized and under reasonable file sizes
5. **Copyright**: Ensure all images are properly licensed for use
