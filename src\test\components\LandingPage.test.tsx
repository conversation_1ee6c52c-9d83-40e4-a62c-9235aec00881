/**
 * @jest-environment jsdom
 */
import LandingPage from '@/components/landing/landing-page';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock the useAuth hook
const mockSignIn = jest.fn().mockResolvedValue({ data: null, error: null });
const mockSignUp = jest.fn().mockResolvedValue({ data: null, error: null });

jest.mock('@/hooks/use-auth', () => ({
  useAuth: () => ({
    signIn: mockSignIn,
    signUp: mockSignUp,
    user: null,
    loading: false,
  }),
}));

// Mock the useRouter hook
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: jest.fn(),
  }),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  TestWrapper.displayName = 'TestWrapper';

  return TestWrapper;
};

describe('LandingPage', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    mockSignIn.mockResolvedValue({ data: null, error: null });
    mockSignUp.mockResolvedValue({ data: null, error: null });
  });

  it('renders the landing page with header and form', () => {
    const Wrapper = createWrapper();

    render(
      <Wrapper>
        <LandingPage />
      </Wrapper>
    );

    // Check header elements
    expect(screen.getByText('Acme Co')).toBeInTheDocument();
    expect(screen.getByText('Product')).toBeInTheDocument();
    expect(screen.getByText('Features')).toBeInTheDocument();
    expect(screen.getByText('Pricing')).toBeInTheDocument();

    // Check form elements (default is sign up)
    expect(screen.getByText('Create Your Account')).toBeInTheDocument();
    expect(screen.getByText('Join Acme and start your journey.')).toBeInTheDocument();
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();

    // Check promotional content
    expect(screen.getByText(/Unlock Your Potential with/)).toBeInTheDocument();
    expect(screen.getByText('Sarah Chen')).toBeInTheDocument();
  });

  it('toggles between sign up and login modes', async () => {
    const Wrapper = createWrapper();

    render(
      <Wrapper>
        <LandingPage />
      </Wrapper>
    );

    // Initially shows sign up form
    expect(screen.getByText('Create Your Account')).toBeInTheDocument();
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();

    // Click "Sign In" link at bottom of form
    const signInLink = screen.getByRole('button', { name: /sign in/i });
    await user.click(signInLink);

    // Should now show login form
    await waitFor(() => {
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      expect(screen.getByText('Sign in to your account to continue.')).toBeInTheDocument();
      expect(screen.queryByLabelText('Full Name')).not.toBeInTheDocument();
    });

    // Click "Sign Up" link to go back
    const signUpLink = screen.getByRole('button', { name: /sign up/i });
    await user.click(signUpLink);

    // Should show sign up form again
    await waitFor(() => {
      expect(screen.getByText('Create Your Account')).toBeInTheDocument();
      expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
    });
  });

  it('toggles mode when header buttons are clicked', async () => {
    const Wrapper = createWrapper();

    render(
      <Wrapper>
        <LandingPage />
      </Wrapper>
    );

    // Click "Log In" button in header
    const headerLogInButton = screen.getAllByText('Log In')[0]; // Get the header button
    await user.click(headerLogInButton);

    // Should show login form
    await waitFor(() => {
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    });

    // Click "Sign Up" button in header
    const headerSignUpButton = screen.getAllByText('Sign Up')[0]; // Get the header button
    await user.click(headerSignUpButton);

    // Should show sign up form
    await waitFor(() => {
      expect(screen.getByText('Create Your Account')).toBeInTheDocument();
    });
  });

  it('validates terms of service for sign up', async () => {
    const Wrapper = createWrapper();

    render(
      <Wrapper>
        <LandingPage />
      </Wrapper>
    );

    // Fill in form fields
    await user.type(screen.getByLabelText('Full Name'), 'John Doe');
    await user.type(screen.getByLabelText('Email Address'), '<EMAIL>');
    await user.type(screen.getByLabelText('Password'), 'password123');

    // Try to submit without agreeing to terms
    const submitButton = screen.getByRole('button', { name: /create account/i });
    await user.click(submitButton);

    // Should show error (mocked toast.error should be called)
    await waitFor(() => {
      const { toast } = require('sonner');
      expect(toast.error).toHaveBeenCalledWith('Please agree to the Terms of Service');
    });
  });

  it('allows form submission when terms are agreed', async () => {
    const Wrapper = createWrapper();

    render(
      <Wrapper>
        <LandingPage />
      </Wrapper>
    );

    // Fill in form fields
    await user.type(screen.getByLabelText('Full Name'), 'John Doe');
    await user.type(screen.getByLabelText('Email Address'), '<EMAIL>');
    await user.type(screen.getByLabelText('Password'), 'password123');

    // Agree to terms
    const termsCheckbox = screen.getByRole('checkbox');
    await user.click(termsCheckbox);

    // Submit form
    const submitButton = screen.getByRole('button', { name: /create account/i });
    await user.click(submitButton);

    // Should call signUp function
    await waitFor(() => {
      expect(mockSignUp).toHaveBeenCalledWith('<EMAIL>', 'password123', {
        name: 'John Doe',
      });
    });
  });

  it('handles login form submission', async () => {
    const Wrapper = createWrapper();

    render(
      <Wrapper>
        <LandingPage />
      </Wrapper>
    );

    // Switch to login mode
    const signInLink = screen.getByRole('button', { name: /sign in/i });
    await user.click(signInLink);

    await waitFor(() => {
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    });

    // Fill in login form
    await user.type(screen.getByLabelText('Email Address'), '<EMAIL>');
    await user.type(screen.getByLabelText('Password'), 'password123');

    // Submit form
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    // Should call signIn function
    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });
  });
});
