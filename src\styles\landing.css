/* Landing Page Custom Styles */

/* Ensure background images are properly displayed */
.landing-bg-image {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Custom form field styling to match Figma design */
.landing-form-field {
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px 16px;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease-in-out;
}

.landing-form-field:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.landing-form-field:hover:not(:focus) {
  border-color: #d1d5db;
}

/* Custom checkbox styling */
.landing-checkbox {
  width: 14px;
  height: 14px;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.landing-checkbox:checked {
  background-image: url('/images/ui/checkbox.png');
  background-size: cover;
  background-position: center;
  border-color: transparent;
}

/* Navigation hover effects */
.nav-link {
  transition: color 0.2s ease-in-out;
}

.nav-link:hover {
  color: #4b5563 !important;
}

/* Button hover effects */
.landing-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.landing-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.landing-button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .landing-content {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }
  
  .landing-hero-section,
  .landing-form-section {
    width: 100%;
    max-width: 500px;
  }
}

@media (max-width: 768px) {
  .landing-hero-section,
  .landing-form-section {
    background-image: none !important;
  }
  
  .landing-hero-section::before,
  .landing-form-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .landing-nav {
    padding: 1rem;
  }
  
  .landing-content {
    padding: 1rem;
    gap: 2rem;
  }
  
  .landing-hero-section,
  .landing-form-section {
    padding: 1.5rem;
    min-height: auto;
  }
  
  .landing-form-field {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 14px 16px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .landing-button,
  .nav-link,
  .landing-link {
    touch-action: manipulation;
  }
  
  .landing-form-field {
    font-size: 16px; /* Prevents zoom on mobile */
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2),
       (min-resolution: 192dpi) {
  .landing-bg-image {
    image-rendering: crisp-edges;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .hero-content-enter,
  .hero-icon-enter,
  .form-field-enter,
  .loading-fade {
    animation: none !important;
    transform: none !important;
  }
}

/* Custom scrollbar for better visual consistency */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation */
.loading-fade {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for form elements */
.form-field-enter {
  animation: slideInUp 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.form-field-enter:nth-child(1) { animation-delay: 0.1s; }
.form-field-enter:nth-child(2) { animation-delay: 0.2s; }
.form-field-enter:nth-child(3) { animation-delay: 0.3s; }
.form-field-enter:nth-child(4) { animation-delay: 0.4s; }
.form-field-enter:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero section animations */
.hero-content-enter {
  animation: fadeInLeft 0.6s ease-out forwards;
  opacity: 0;
  transform: translateX(-30px);
}

.hero-icon-enter {
  animation: bounceIn 0.8s ease-out forwards;
  opacity: 0;
  transform: scale(0.8);
  animation-delay: 0.2s;
}

@keyframes fadeInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading state for submit button */
.button-loading {
  position: relative;
  color: transparent !important;
}

.button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced focus states for accessibility */
.landing-checkbox:focus-visible {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Link hover effects */
.landing-link {
  position: relative;
  transition: color 0.2s ease-in-out;
}

.landing-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.3s ease-in-out;
}

.landing-link:hover::after {
  width: 100%;
}
