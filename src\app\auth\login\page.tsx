"use client";

import { AuthForm } from "@/components/auth/auth-form";
import { useState } from "react";

export default function LoginPage() {
  const [mode, setMode] = useState<"signin" | "signup">("signin");

  const toggleMode = () => {
    setMode(mode === "signin" ? "signup" : "signin");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="w-full max-w-md">
        {/* EduPro Branding */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            EduPro
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Your comprehensive educational platform
          </p>
        </div>
        
        {/* Auth Form */}
        <AuthForm mode={mode} onToggleMode={toggleMode} />
        
        {/* Additional Links */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            By continuing, you agree to our{" "}
            <a href="#" className="text-blue-600 hover:underline dark:text-blue-400">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="text-blue-600 hover:underline dark:text-blue-400">
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}