import { createClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

export async function GET() {
  const checks = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    checks: {
      database: false,
      auth: false,
    },
  };

  try {
    // Check Supabase connection
    const supabase = await createClient();
    
    // Test database connection
    try {
      const { error: dbError } = await supabase
        .from('profiles')
        .select('count', { count: 'exact', head: true });
      
      checks.checks.database = !dbError;
    } catch (error) {
      console.error('Database health check failed:', error);
      checks.checks.database = false;
    }

    // Test auth service
    try {
      const { error: authError } = await supabase.auth.getSession();
      checks.checks.auth = !authError;
    } catch (error) {
      console.error('Auth health check failed:', error);
      checks.checks.auth = false;
    }

    // Determine overall status
    const allChecksPass = Object.values(checks.checks).every(check => check === true);
    checks.status = allChecksPass ? 'ok' : 'degraded';

    return NextResponse.json(checks, {
      status: allChecksPass ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        checks: {
          database: false,
          auth: false,
        },
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    );
  }
}
