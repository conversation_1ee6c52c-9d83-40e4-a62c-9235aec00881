import { AuthForm } from '@/components/auth/auth-form';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  TestWrapper.displayName = 'TestWrapper';

  return TestWrapper;
};

describe('AuthForm', () => {
  const user = userEvent.setup();
  const mockOnToggleMode = jest.fn();

  beforeEach(() => {
    mockOnToggleMode.mockClear();
  });

  it('renders signin form', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <AuthForm mode="signin" onToggleMode={mockOnToggleMode} />
      </Wrapper>
    );

    expect(screen.getByText('Welcome back')).toBeInTheDocument();
    expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('renders signup form', () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <AuthForm mode="signup" onToggleMode={mockOnToggleMode} />
      </Wrapper>
    );

    expect(screen.getByText('Create an account')).toBeInTheDocument();
    expect(screen.getByText('Enter your details to get started')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    const Wrapper = createWrapper();
    
    render(
      <Wrapper>
        <AuthForm mode="signin" onToggleMode={mockOnToggleMode} />
      </Wrapper>
    );

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
    });
  });
});
