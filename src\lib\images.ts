/**
 * Image path utilities for type-safe image imports
 */

// Base path for all images
const IMAGES_BASE_PATH = '/images';

// Image directory paths
export const IMAGE_PATHS = {
  auth: `${IMAGES_BASE_PATH}/auth`,
  avatars: `${IMAGES_BASE_PATH}/avatars`,
  backgrounds: `${IMAGES_BASE_PATH}/backgrounds`,
  courses: `${IMAGES_BASE_PATH}/courses`,
  hero: `${IMAGES_BASE_PATH}/hero`,
  icons: `${IMAGES_BASE_PATH}/icons`,
  logos: `${IMAGES_BASE_PATH}/logos`,
  ui: `${IMAGES_BASE_PATH}/ui`,
} as const;

// Common image file extensions
export type ImageExtension = 'jpg' | 'jpeg' | 'png' | 'svg' | 'webp' | 'gif';

// Image categories
export type ImageCategory = keyof typeof IMAGE_PATHS;

/**
 * Utility function to construct image paths
 * @param category - The image category/directory
 * @param filename - The image filename (with or without extension)
 * @param extension - The file extension (optional if included in filename)
 * @returns Complete image path
 */
export function getImagePath(
  category: ImageCategory,
  filename: string,
  extension?: ImageExtension
): string {
  const basePath = IMAGE_PATHS[category];
  const fullFilename = extension && !filename.includes('.') 
    ? `${filename}.${extension}` 
    : filename;
  
  return `${basePath}/${fullFilename}`;
}

/**
 * Predefined common image paths for the application
 */
export const COMMON_IMAGES = {
  // Logos
  logo: getImagePath('logos', 'edupro-logo.svg'),
  logoLight: getImagePath('logos', 'edupro-logo-light.svg'),
  logoDark: getImagePath('logos', 'edupro-logo-dark.svg'),
  
  // Default avatars
  defaultAvatar: getImagePath('avatars', 'default-avatar.png'),
  
  // Auth backgrounds
  loginBackground: getImagePath('auth', 'login-background.jpg'),
  signupBackground: getImagePath('auth', 'signup-background.jpg'),
  
  // Hero images
  heroImage: getImagePath('hero', 'main-hero.jpg'),
  welcomeBanner: getImagePath('hero', 'welcome-banner.jpg'),
  
  // Common icons
  loadingSpinner: getImagePath('icons', 'loading.svg'),
  errorIcon: getImagePath('icons', 'error.svg'),
  successIcon: getImagePath('icons', 'success.svg'),
} as const;

/**
 * Helper function to get course thumbnail
 * @param courseSlug - The course identifier/slug
 * @param extension - Image extension (default: 'jpg')
 * @returns Course thumbnail path
 */
export function getCourseImage(courseSlug: string, extension: ImageExtension = 'jpg'): string {
  return getImagePath('courses', `${courseSlug}-thumbnail`, extension);
}

/**
 * Helper function to get user avatar
 * @param userId - The user identifier
 * @param extension - Image extension (default: 'jpg')
 * @returns User avatar path
 */
export function getUserAvatar(userId: string, extension: ImageExtension = 'jpg'): string {
  return getImagePath('avatars', `user-${userId}`, extension);
}
