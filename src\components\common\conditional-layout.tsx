"use client";

import { usePathname } from "next/navigation";
import Footer from "./footer";
import Header from "./header";

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  
  // Routes that should not show header and footer
  const fullScreenRoutes = ["/"];
  
  const isFullScreen = fullScreenRoutes.includes(pathname);
  
  if (isFullScreen) {
    return <>{children}</>;
  }
  
  return (
    <div className="relative flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  );
}
