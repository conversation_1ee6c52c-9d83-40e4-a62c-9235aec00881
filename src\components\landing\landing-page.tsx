"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/hooks/use-auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

// Header Component
function Header({ onSignUpClick, onLogInClick }: { onSignUpClick: () => void; onLogInClick: () => void }) {
  return (
    <header className="bg-slate-900 text-white px-6 py-4">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <span className="text-xl font-semibold">Acme Co</span>
        </div>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <Link href="#" className="text-gray-300 hover:text-white transition-colors">
            Product
          </Link>
          <Link href="#" className="text-gray-300 hover:text-white transition-colors">
            Features
          </Link>
          <Link href="#" className="text-gray-300 hover:text-white transition-colors">
            Pricing
          </Link>
        </nav>

        {/* Auth Buttons */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onSignUpClick}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Sign Up
          </button>
          <button
            onClick={onLogInClick}
            className="text-gray-300 hover:text-white px-4 py-2 font-medium transition-colors"
          >
            Log In
          </button>
        </div>
      </div>
    </header>
  );
}

export default function LandingPage() {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(true); // Toggle between sign up and login
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const router = useRouter();
  const { signIn, signUp } = useAuth();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (isSignUp) {
        if (!agreeToTerms) {
          toast.error("Please agree to the Terms of Service");
          return;
        }
        const { error } = await signUp(formData.email, formData.password, {
          name: formData.fullName,
        });

        if (error) {
          toast.error(error.message || "Failed to create account");
          return;
        }

        toast.success("Account created successfully!");
      } else {
        const { error } = await signIn(formData.email, formData.password);

        if (error) {
          toast.error(error.message || "Failed to sign in");
          return;
        }

        toast.success("Logged in successfully!");
      }

      router.push("/dashboard");
    } catch (err) {
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header
        onSignUpClick={() => setIsSignUp(true)}
        onLogInClick={() => setIsSignUp(false)}
      />

      {/* Main Content */}
      <div className="flex min-h-[calc(100vh-80px)]">
        {/* Left Side - Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8 lg:p-16 bg-gray-50">
          <div className="w-full max-w-md">
            {/* Form Header */}
            <div className="mb-8">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                {isSignUp ? "Create Your Account" : "Welcome Back"}
              </h1>
              <p className="text-gray-600">
                {isSignUp ? "Join Acme and start your journey." : "Sign in to your account to continue."}
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-5">
              {/* Full Name Field - Only for Sign Up */}
              {isSignUp && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name
                  </label>
                  <Input
                    type="text"
                    name="fullName"
                    placeholder="e.g., Alex Johnson"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    disabled={isLoading}
                    required={isSignUp}
                  />
                </div>
              )}

              {/* Email Field */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <Input
                  type="email"
                  name="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  disabled={isLoading}
                  required
                />
              </div>

              {/* Password Field */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <Input
                  type="password"
                  name="password"
                  placeholder="Enter a strong password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  disabled={isLoading}
                  required
                />
              </div>

              {/* Terms of Service - Only for Sign Up */}
              {isSignUp && (
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="terms-of-service"
                    checked={agreeToTerms}
                    onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
                    className="mt-1"
                  />
                  <label htmlFor="terms-of-service" className="text-sm text-gray-700 leading-5">
                    I agree to the{" "}
                    <Link href="#" className="text-blue-600 hover:underline font-medium">
                      Terms of Service
                    </Link>
                  </label>
                </div>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading || (isSignUp && !agreeToTerms)}
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading
                  ? (isSignUp ? "Creating Account..." : "Signing In...")
                  : (isSignUp ? "Create Account" : "Sign In")
                }
              </Button>

              {/* Toggle between Sign Up and Login */}
              <div className="text-center pt-4">
                <p className="text-sm text-gray-600">
                  {isSignUp ? "Already have an account? " : "Don't have an account? "}
                  <button
                    type="button"
                    onClick={() => setIsSignUp(!isSignUp)}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {isSignUp ? "Sign In" : "Sign Up"}
                  </button>
                </p>
              </div>
            </form>
          </div>
        </div>

        {/* Right Side - Promotional Content */}
        <div className="hidden lg:flex w-1/2 bg-gradient-to-br from-blue-600 via-purple-600 to-purple-700 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-full"></div>
            <div className="absolute bottom-20 right-20 w-24 h-24 bg-white rounded-full"></div>
            <div className="absolute top-1/2 right-1/3 w-16 h-16 bg-white rounded-full"></div>
          </div>

          <div className="relative z-10 p-16 flex flex-col justify-center text-white">
            {/* Icon */}
            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-8">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>

            {/* Main Content */}
            <h2 className="text-4xl font-bold mb-6 leading-tight">
              Unlock Your Potential with<br />
              <span className="text-blue-200">Acme Co.</span>
            </h2>
            <p className="text-xl text-blue-100 mb-12 leading-relaxed">
              Experience a seamless platform designed to elevate your productivity and drive success.
            </p>

            {/* Testimonial */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-pink-400 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-semibold text-lg">SC</span>
                </div>
                <div>
                  <p className="font-semibold text-white">Sarah Chen</p>
                  <p className="text-sm text-blue-200">Marketing Director, Innovatech</p>
                </div>
              </div>
              <div className="flex mb-3">
                <svg className="w-6 h-6 text-blue-200 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                </svg>
              </div>
              <p className="text-blue-100 italic leading-relaxed">
                "Acme Co has revolutionized how we manage our projects. Their platform is intuitive, powerful, and has significantly boosted our team's productivity. Highly recommended!"
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
