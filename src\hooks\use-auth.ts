"use client";

import { createClient } from "@/lib/supabase/client";
import { useAuthStore } from "@/store/auth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export function useAuth() {
  const router = useRouter();
  const { user, setUser, setLoading, loading } = useAuthStore();
  const supabase = createClient();

  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
        const supabaseUser = session?.user;
        if (supabaseUser) {
          // Convert Supabase user to our User type
          const appUser = {
            id: supabaseUser.id,
            email: supabaseUser.email || "",
            name: supabaseUser.user_metadata?.name,
            avatar_url: supabaseUser.user_metadata?.avatar_url,
            created_at: supabaseUser.created_at,
            updated_at: supabaseUser.updated_at || supabaseUser.created_at,
          };
          setUser(appUser);
        } else {
          setUser(null);
        }
        setLoading(false);
      } else if (event === "SIGNED_OUT") {
        setUser(null);
        setLoading(false);
        router.push("/auth/login");
      }
    });

    // Get initial session
    const getSession = async () => {
      setLoading(true);
      const {
        data: { session },
      } = await supabase.auth.getSession();
      
      const supabaseUser = session?.user;
      if (supabaseUser) {
        // Convert Supabase user to our User type
        const appUser = {
          id: supabaseUser.id,
          email: supabaseUser.email || "",
          name: supabaseUser.user_metadata?.name,
          avatar_url: supabaseUser.user_metadata?.avatar_url,
          created_at: supabaseUser.created_at,
          updated_at: supabaseUser.updated_at || supabaseUser.created_at,
        };
        setUser(appUser);
      } else {
        setUser(null);
      }
      setLoading(false);
    };

    getSession();

    return () => subscription.unsubscribe();
  }, [router, setUser, setLoading, supabase.auth]);

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    setLoading(false);
    return { data, error };
  };

  const signUp = async (email: string, password: string, metadata?: Record<string, unknown>) => {
    setLoading(true);
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      ...(metadata && {
        options: {
          data: metadata
        }
      })
    });
    setLoading(false);
    return { data, error };
  };

  const signOut = async () => {
    setLoading(true);
    const { error } = await supabase.auth.signOut();
    setLoading(false);
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    return { data, error };
  };

  const updatePassword = async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password,
    });
    return { data, error };
  };

  return {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
  };
}