"use client";

import { useAuth } from "@/hooks/use-auth";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function LandingPageFigma() {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    agreeToTerms: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { signUp } = useAuth();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      agreeToTerms: checked
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.agreeToTerms) {
      toast.error("Please agree to the Terms of Service");
      return;
    }

    setIsLoading(true);

    try {
      const { error } = await signUp(formData.email, formData.password, {
        name: formData.fullName,
      });

      if (error) {
        throw error;
      }

      toast.success("Account created successfully! Please check your email to verify your account.");
      router.push("/dashboard");
    } catch (error: any) {
      console.error("Sign up error:", error);
      toast.error(error.message || "Failed to create account. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="relative w-[1206px] h-[742px] mx-auto" style={{ backgroundColor: '#ffffff' }}>
      {/* Header Background */}
      <div className="absolute top-0 left-0 w-[1206px] h-[73px]">
        <Image
          src="/images/figma/header-background.png"
          alt="Header Background"
          fill
          className="object-cover"
          priority
        />
      </div>

      {/* Navigation */}
      <div className="absolute top-0 left-0 w-[1206px] h-[74px] z-10">
        {/* Brand Logo */}
        <div className="absolute left-[29px] top-[23px] w-[25px] h-[25px]">
          <Image
            src="/images/figma/brand-logo.png"
            alt="Acme Co Logo"
            width={25}
            height={25}
          />
        </div>

        {/* Brand Name */}
        <div 
          className="absolute left-[63px] top-[22px] w-[101px] h-[25px] font-inter font-semibold text-[22.4px] leading-[27px] text-center"
          style={{ color: '#A7ACB7' }}
        >
          Acme Co
        </div>

        {/* Navigation Links */}
        <div 
          className="absolute left-[754px] top-[25px] w-[57px] h-[19px] font-inter font-normal text-[14.8px] leading-[18px] text-left cursor-pointer"
          style={{ color: '#7D8290' }}
        >
          Product
        </div>

        <div 
          className="absolute left-[832px] top-[25px] w-[61px] h-[19px] font-inter font-normal text-[14.5px] leading-[18px] text-left cursor-pointer"
          style={{ color: '#7C818F' }}
        >
          Features
        </div>

        <div 
          className="absolute left-[913px] top-[25px] w-[50px] h-[21px] font-inter font-normal text-[14.9px] leading-[18px] text-left cursor-pointer"
          style={{ color: '#747A88' }}
        >
          Pricing
        </div>

        {/* Log In Button */}
        <div className="absolute left-[983px] top-[11px] w-[200px] h-[47px]">
          <div 
            className="absolute left-[129px] top-[14px] w-[45px] h-[21px] font-inter font-normal text-[14.9px] leading-[18px] text-left cursor-pointer"
            style={{ color: '#959AA4' }}
          >
            Log In
          </div>
        </div>

        {/* Sign Up Button Background */}
        <div className="absolute left-[985px] top-[13px] w-[197px] h-[45px]">
          <Image
            src="/images/figma/sign-up-button-bg.png"
            alt="Sign Up Button Background"
            fill
            className="object-cover"
          />
        </div>

        {/* Sign Up Button */}
        <div className="absolute left-[983px] top-[11px] w-[109px] h-[47px]">
          <div className="absolute left-[3px] top-[2px] w-[106px] h-[45px] rounded-[23.5px_24.5px_21.75px_20px]">
            <Image
              src="/images/figma/sign-up-button-bg.png"
              alt="Sign Up Button"
              fill
              className="object-cover rounded-[23.5px_24.5px_21.75px_20px]"
            />
          </div>
          <div 
            className="absolute left-[28px] top-[13px] w-[57px] h-[21px] font-inter font-normal text-[14.9px] leading-[18px] text-left"
            style={{ color: '#B4B1F2' }}
          >
            Sign Up
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute top-[72px] left-0 w-[1206px] h-[670px]">
        {/* Main Background */}
        <div className="absolute top-0 left-0 w-[1206px] h-[670px]">
          <Image
            src="/images/figma/main-background.png"
            alt="Main Background"
            fill
            className="object-cover"
          />
        </div>

        {/* Hero Section */}
        <div className="absolute top-[40px] left-[604px] w-[422px] h-[534px]">
          {/* Hero Background */}
          <div className="absolute top-0 left-0 w-[422px] h-[534px]">
            <Image
              src="/images/figma/hero-section-bg.png"
              alt="Hero Section Background"
              fill
              className="object-cover"
            />
          </div>

          {/* Hero Icon */}
          <div className="absolute top-[40px] left-[39px] w-[58px] h-[65px] rounded-[125px]">
            <Image
              src="/images/figma/hero-icon.png"
              alt="Hero Icon"
              width={58}
              height={65}
              className="rounded-[125px]"
            />
          </div>

          {/* Hero Title */}
          <div 
            className="absolute top-[118px] left-[38.25px] w-[297px] h-[60px] font-inter font-semibold text-[22.9px] leading-[27px] text-left"
            style={{ color: '#7872D6' }}
          >
            Unlock Your Potential with<br />Acme Co.
          </div>

          {/* Hero Subtitle */}
          <div 
            className="absolute top-[186px] left-[38px] w-[318px] h-[51px] font-inter font-normal text-[15px] leading-[22px] text-left"
            style={{ color: '#9CA3AE' }}
          >
            Experience a seamless platform designed to<br />elevate your productivity and drive success.
          </div>
        </div>

        {/* Form Section */}
        <div className="absolute top-[41px] left-[183px] w-[417px] h-[532px]">
          {/* Form Background */}
          <div className="absolute top-[-5px] left-[-5px] w-[426px] h-[542px]">
            <Image
              src="/images/figma/form-background.png"
              alt="Form Background"
              fill
              className="object-cover"
            />
          </div>

          {/* Form Content */}
          <form onSubmit={handleSubmit} className="relative z-10">
            {/* Form Title */}
            <div 
              className="absolute top-[105px] left-[39px] w-[194px] h-[22px] font-inter font-medium text-[18.9px] leading-[23px] text-left"
              style={{ color: '#6C717D' }}
            >
              Create Your Account
            </div>

            {/* Form Subtitle */}
            <div 
              className="absolute top-[134px] left-[39px] w-[208px] h-[18px] font-inter font-normal text-[12.7px] leading-[15px] text-left"
              style={{ color: '#B4BAC5' }}
            >
              Join Acme and start your journey.
            </div>

            {/* Full Name Field */}
            <div className="absolute top-[185px] left-[34px] w-[354px] h-[45px]">
              <div 
                className="absolute top-[-15px] left-[5px] w-[55px] h-[14px] font-inter font-normal text-[11px] leading-[13px] text-left"
                style={{ color: '#979DA7' }}
              >
                Full Name
              </div>
              <input
                type="text"
                name="fullName"
                value={formData.fullName}
                onChange={handleInputChange}
                className="w-full h-full bg-transparent border-none outline-none px-[10px] py-[15px] font-inter text-[14px]"
                style={{ color: '#6C717D' }}
                required
              />
            </div>

            {/* Email Address Field */}
            <div className="absolute top-[251px] left-[34px] w-[354px] h-[44px]">
              <div 
                className="absolute top-[-15px] left-[5px] w-[78px] h-[15px] font-inter font-normal text-[11px] leading-[13px] text-left"
                style={{ color: '#999EA8' }}
              >
                Email Address
              </div>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full h-full bg-transparent border-none outline-none px-[10px] py-[15px] font-inter text-[14px]"
                style={{ color: '#6C717D' }}
                required
              />
            </div>

            {/* Password Field */}
            <div className="absolute top-[317px] left-[34px] w-[354px] h-[44px]">
              <div 
                className="absolute top-[-15px] left-[5px] w-[54px] h-[14px] font-inter font-semibold text-[11px] leading-[13px] text-left"
                style={{ color: '#9AA0A9' }}
              >
                Password
              </div>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="w-full h-full bg-transparent border-none outline-none px-[10px] py-[15px] font-inter text-[14px]"
                style={{ color: '#6C717D' }}
                required
              />
            </div>

            {/* Terms Checkbox */}
            <div className="absolute top-[367px] left-[39px] flex items-center">
              <div className="w-[14px] h-[14px] relative cursor-pointer" onClick={() => handleCheckboxChange(!formData.agreeToTerms)}>
                <Image
                  src="/images/figma/checkbox.png"
                  alt="Checkbox"
                  width={14}
                  height={14}
                />
                {formData.agreeToTerms && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-[8px] h-[8px] bg-[#7872D6] rounded-sm"></div>
                  </div>
                )}
              </div>
              <div 
                className="ml-[7px] font-inter font-light text-[11px] leading-[13px] text-left"
                style={{ color: '#A2A7B0' }}
              >
                I agree to the{' '}
                <span 
                  className="font-inter font-normal text-[10.7px] leading-[13px] underline cursor-pointer"
                  style={{ color: '#ABA8F0' }}
                >
                  Terms of Service
                </span>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="absolute top-[390px] left-[34px] w-[353px] h-[43px] bg-transparent border-none outline-none cursor-pointer disabled:opacity-50"
            >
              <div className="w-full h-full relative">
                <Image
                  src="/images/figma/sign-up-button-bg.png"
                  alt="Sign Up Button"
                  fill
                  className="object-cover rounded-[23.5px_24.5px_21.75px_20px]"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span 
                    className="font-inter font-normal text-[14.9px] leading-[18px]"
                    style={{ color: '#B4B1F2' }}
                  >
                    {isLoading ? "Creating Account..." : "Sign Up"}
                  </span>
                </div>
              </div>
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
