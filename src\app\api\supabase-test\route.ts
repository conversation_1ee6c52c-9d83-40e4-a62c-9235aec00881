import { createClient } from "@/lib/supabase/server";
import { NextResponse } from "next/server";

/**
 * API endpoint to test Supabase connection
 * GET /api/supabase-test
 */
export async function GET() {
  try {
    // Create server client
    const supabase = await createClient();
    
    // Test basic connection by trying to get the current user
    // This is a lightweight operation that tests the connection without requiring any tables
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    // The auth.getUser() call should succeed even without authentication
    // A network/connection error would be thrown in the catch block
    
    return NextResponse.json({
      success: true,
      message: 'Successfully connected to Supabase',
      url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      projectId: process.env.SUPABASE_PROJECT_ID,
      authStatus: userError ? 'No authenticated user (this is expected for anonymous access)' : `User data available: ${!!userData.user}`,
      connectionTest: 'Auth endpoint accessible'
    });
  } catch (error) {
    console.error('Supabase connection test error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Error testing Supabase connection',
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Configured' : 'Missing',
          anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configured' : 'Missing',
          endpoint: process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not available'
        }
      },
      { status: 500 }
    );
  }
}
