{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"react/no-unescaped-entities": "off", "@next/next/no-page-custom-font": "off", "prefer-const": "error", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn"}, "overrides": [{"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser"}, {"files": ["**/*.test.ts", "**/*.test.tsx"], "env": {"jest": true}}]}