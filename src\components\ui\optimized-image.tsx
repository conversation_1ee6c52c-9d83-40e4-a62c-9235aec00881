'use client';

import { COMMON_IMAGES } from '@/lib/images';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { useState } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackSrc?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Optimized image component with error handling and fallback support
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  fallbackSrc,
  priority = false,
  fill = false,
  sizes,
  objectFit = 'cover',
  placeholder = 'empty',
  blurDataURL,
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    
    if (fallbackSrc && imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
    }
    
    onError?.();
  };

  const imageProps = {
    src: imgSrc,
    alt,
    className: cn(
      'transition-opacity duration-300',
      isLoading && 'opacity-0',
      !isLoading && 'opacity-100',
      className
    ),
    onLoad: handleLoad,
    onError: handleError,
    priority,
    placeholder,
    blurDataURL,
    style: { objectFit },
    ...(sizes && { sizes }),
  };

  if (fill) {
    return <Image {...imageProps} fill />;
  }

  return (
    <Image
      {...imageProps}
      width={width || 400}
      height={height || 300}
    />
  );
}

/**
 * Avatar component with default fallback
 */
interface AvatarImageProps {
  src?: string;
  alt: string;
  size?: number;
  className?: string;
}

export function AvatarImage({ 
  src, 
  alt, 
  size = 40, 
  className 
}: AvatarImageProps) {
  return (
    <OptimizedImage
      src={src || COMMON_IMAGES.defaultAvatar}
      alt={alt}
      width={size}
      height={size}
      className={cn('rounded-full', className)}
      fallbackSrc={COMMON_IMAGES.defaultAvatar}
    />
  );
}

/**
 * Course thumbnail component
 */
interface CourseThumbnailProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
}

export function CourseThumbnail({ 
  src, 
  alt, 
  width = 300, 
  height = 200, 
  className 
}: CourseThumbnailProps) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={cn('rounded-lg', className)}
      objectFit="cover"
    />
  );
}

/**
 * Logo component with theme support
 */
interface LogoProps {
  variant?: 'default' | 'light' | 'dark';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Logo({ variant = 'default', size = 'md', className }: LogoProps) {
  const sizeMap = {
    sm: { width: 120, height: 40 },
    md: { width: 160, height: 50 },
    lg: { width: 200, height: 60 },
  };

  const logoSrc = variant === 'light' 
    ? COMMON_IMAGES.logoLight 
    : variant === 'dark' 
    ? COMMON_IMAGES.logoDark 
    : COMMON_IMAGES.logo;

  return (
    <OptimizedImage
      src={logoSrc}
      alt="EduPro Logo"
      width={sizeMap[size].width}
      height={sizeMap[size].height}
      className={className}
      priority
    />
  );
}
