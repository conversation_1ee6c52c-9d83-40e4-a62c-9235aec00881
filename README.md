# EduPro

EduPro is a full-stack educational platform built with Next.js, React, and Supabase. This project leverages modern technologies to provide a seamless user experience for authentication, dashboard management, and responsive design.

## Technology Stack

- **Next.js 15+**: A React framework for building server-rendered applications.
- **React 19+**: A JavaScript library for building user interfaces.
- **TypeScript**: A superset of JavaScript that adds static types.
- **Tailwind CSS v4**: A utility-first CSS framework for rapid UI development.
- **Supabase**: An open-source Firebase alternative for database and authentication.
- **Shadcn/ui**: A component library for building UI elements with ease.
- **Turbopack**: A fast build tool for modern web applications.

## Project Structure

```
edupro
├── src
│   ├── app
│   ├── components
│   ├── lib
│   ├── hooks
│   └── types
├── public
├── components.json
├── package.json
├── tailwind.config.ts
├── tsconfig.json
├── next.config.js
└── README.md
```

## Setup Instructions

1. **Clone the Repository**
   ```bash
   git clone https://github.com/yourusername/edupro.git
   cd edupro
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Configure Environment Variables**
   Create a `.env.local` file in the root directory and add your Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Run the Development Server**
   ```bash
   npm run dev
   ```

5. **Open Your Browser**
   Navigate to `http://localhost:3000` to view the application.

## Features

- User authentication with login and signup functionality.
- A dashboard for authenticated users.
- Responsive design using Tailwind CSS.
- Custom UI components built with Shadcn/ui.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.